<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';
require_once 'classes/IntranetAPI.php';

// Adicionar CSS personalizado para cores da identidade visual Sicoob
echo '<style>
:root {
    --sicoob-verde-escuro: #003641;
    --sicoob-verde-medio: #00AE9D;
    --sicoob-verde-claro: #C9D200;
    --sicoob-turquesa: #00AE9D;
    --sicoob-branco: #FFFFFF;
    --sicoob-cinza: #58595B;
}

/* Badges personalizados com cores da identidade visual Sicoob */
.bg-sicoob-turquesa {
    background-color: var(--sicoob-turquesa) !important;
    color: var(--sicoob-branco) !important;
}

.bg-sicoob-verde-claro {
    background-color: var(--sicoob-verde-claro) !important;
    color: var(--sicoob-verde-escuro) !important;
    font-weight: 600;
}

/* Personalização do accordion das trilhas com cores Sicoob */
.accordion-item {
    border: 1px solid rgba(0, 174, 157, 0.2) !important;
    margin-bottom: 0.5rem;
    border-radius: 8px !important;
    overflow: hidden;
}

.accordion-button {
    background-color: transparent !important;
    border: none !important;
    color: var(--sicoob-verde-escuro) !important;
}

.accordion-button:not(.collapsed) {
    background-color: rgba(0, 174, 157, 0.1) !important;
    color: var(--sicoob-verde-escuro) !important;
    border-color: rgba(0, 174, 157, 0.3) !important;
    box-shadow: none !important;
}

.accordion-button:focus {
    border-color: rgba(0, 174, 157, 0.4) !important;
    box-shadow: 0 0 0 0.25rem rgba(0, 174, 157, 0.15) !important;
}

.accordion-button:hover {
    background-color: rgba(0, 174, 157, 0.05) !important;
}

.accordion-collapse.show {
    border-top: 1px solid rgba(0, 174, 157, 0.2) !important;
}
</style>';

// Verificar se CPF foi fornecido
if (empty($_GET['cpf'])) {
    echo '<div class="alert alert-danger">CPF não fornecido.</div>';
    exit;
}

$cpf = str_pad(preg_replace('/[^0-9]/', '', $_GET['cpf']), 11, '0', STR_PAD_LEFT);

// Inicializar API da Intranet
$api = new IntranetAPI();

// Buscar configurações de prazos personalizados
$stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
$prazos_config = [];
foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
    $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
    $prazos_config[$key] = $config;
}

// Buscar trilhas obrigatórias (com verificação se a tabela existe)
$trilhas_obrigatorias = [];
try {
    $stmt_trilhas_obrigatorias = $pdo_edu->query("SELECT codigo_trilha, trilha, obrigatoria FROM edu_trilhas_obrigatorias WHERE obrigatoria = 1");
    foreach ($stmt_trilhas_obrigatorias->fetchAll(PDO::FETCH_ASSOC) as $trilha_obrig) {
        $trilhas_obrigatorias[$trilha_obrig['codigo_trilha']] = $trilha_obrig;
    }
} catch (PDOException $e) {
    // Tabela ainda não existe, continuar sem informações de trilhas obrigatórias
    $trilhas_obrigatorias = [];
}

// Buscar dados do colaborador na base de educação
$query_colaborador = "
    SELECT
        cpf,
        usuario,
        email,
        funcao,
        codigo_unidade,
        hierarquia_unidade,
        data_admissao,
        superior_imediato,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT recurso) as total_cursos,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
        SUM(CASE WHEN situacao_trilha = 'Concluída' THEN 1 ELSE 0 END) as trilhas_concluidas,
        0 as cursos_a_vencer,
        0 as cursos_vencidos,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    WHERE cpf = ?
    GROUP BY cpf, usuario, email, funcao, codigo_unidade, hierarquia_unidade, data_admissao, superior_imediato
";

$stmt = $pdo_edu->prepare($query_colaborador);
$stmt->execute([$cpf]);
$colaborador = $stmt->fetch();

if (!$colaborador) {
    echo '<div class="alert alert-warning">Colaborador não encontrado.</div>';
    exit;
}

// Verificar se o colaborador é APRENDIZ
if ($colaborador['funcao'] === 'APRENDIZ') {
    echo '<div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i>
        <strong>Acesso Restrito:</strong> Detalhes de colaboradores com função APRENDIZ não são exibidos.
    </div>';
    exit;
}

// Buscar dados da Intranet - APENAS USUÁRIOS ATIVOS
$usuario_intranet = null;
$usuarios_intranet = $api->listarUsuariosAtivos();
if ($usuarios_intranet !== false) {
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_intranet = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            if ($cpf_intranet === $cpf) {
                $usuario_intranet = $usuario;
                break;
            }
        }
    }
}

// Buscar agências da Intranet
$agencias_intranet = $api->listarAgencias();
$mapa_agencias = [];
if ($agencias_intranet !== false) {
    foreach ($agencias_intranet as $agencia) {
        if (!empty($agencia['id'])) {
            $mapa_agencias[$agencia['id']] = $agencia;
        }
    }
}

// Buscar trilhas do colaborador
$query_trilhas = "
    SELECT 
        trilha,
        codigo_trilha,
        situacao_trilha,
        aprovado_trilha,
        aproveitamento,
        iniciar_trilha_em,
        concluir_trilha_ate,
        data_aprovacao_trilha,
        COUNT(DISTINCT recurso) as total_cursos_trilha,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados_trilha
    FROM edu_relatorio_educacao
    WHERE cpf = ?
    GROUP BY trilha, codigo_trilha, situacao_trilha, aprovado_trilha, aproveitamento, 
             iniciar_trilha_em, concluir_trilha_ate, data_aprovacao_trilha
    ORDER BY trilha
";

$stmt_trilhas = $pdo_edu->prepare($query_trilhas);
$stmt_trilhas->execute([$cpf]);
$trilhas = $stmt_trilhas->fetchAll();

// Buscar cursos do colaborador
$query_cursos = "
    SELECT
        trilha,
        codigo_trilha,
        recurso,
        codigo_recurso,
        aprovacao,
        nota_recurso,
        aproveitamento,
        carga_horaria_recurso,
        data_conclusao,
        validade_recurso,
        andamento_etapa,
        concluir_trilha_ate,
        data_admissao
    FROM edu_relatorio_educacao
    WHERE cpf = ?
    ORDER BY trilha, recurso
";

$stmt_cursos = $pdo_edu->prepare($query_cursos);
$stmt_cursos->execute([$cpf]);
$cursos_raw = $stmt_cursos->fetchAll();

// Calcular prazos para cada curso
$cursos = [];
foreach ($cursos_raw as $curso) {
    $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];

    if (isset($prazos_config[$key])) {
        // Verificar se colaborador é elegível para prazo personalizado (admitido após 01/01/2023)
        $data_corte = '2023-01-01';
        $elegivel_prazo_personalizado = ($curso['data_admissao'] > $data_corte);

        if ($elegivel_prazo_personalizado) {
            // Usar prazo personalizado
            $prazo_calculado = calcularPrazoPersonalizado(
                $cpf,
                $curso['codigo_trilha'],
                $curso['codigo_recurso'],
                $curso['data_admissao'],
                $curso['concluir_trilha_ate'],
                $pdo_edu
            );

            if ($prazo_calculado === null) {
                // Curso concluído e sem renovação
                $curso['prazo_calculado'] = null;
                $curso['prazo_personalizado'] = true;
                $curso['motivo_prazo'] = 'Curso concluído (sem renovação)';
            } else {
                $curso['prazo_calculado'] = $prazo_calculado;
                $curso['prazo_personalizado'] = true;
                $curso['motivo_prazo'] = 'Prazo personalizado aplicado';
            }
        } else {
            // Usar prazo padrão (colaborador não elegível)
            $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
            $curso['prazo_personalizado'] = false;
            $curso['motivo_prazo'] = 'Prazo padrão (admitido antes de 01/01/2023)';
        }
    } else {
        // NOVA REGRA: Cursos sem prazo personalizado não têm renovação
        if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
            // Curso já foi concluído - sem prazo (não precisa refazer)
            $curso['prazo_calculado'] = null;
            $curso['prazo_personalizado'] = false;
            $curso['motivo_prazo'] = 'Curso concluído (sem renovação)';
        } else {
            // Curso ainda não concluído - usar prazo padrão
            $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
            $curso['prazo_personalizado'] = false;
            $curso['motivo_prazo'] = 'Prazo padrão (sem configuração personalizada)';
        }
    }

    // Calcular status do prazo
    if ($curso['prazo_calculado'] === null) {
        // Curso sem prazo (concluído sem renovação)
        if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
            $curso['status_prazo'] = 'concluido_sem_renovacao';
            $curso['dias_prazo'] = null;
            $curso['classe_prazo'] = 'success';
            $curso['texto_prazo'] = 'Concluído';
        } else {
            $curso['status_prazo'] = 'sem_prazo';
            $curso['dias_prazo'] = null;
            $curso['classe_prazo'] = 'secondary';
            $curso['texto_prazo'] = 'Sem prazo definido';
        }
    } elseif (!empty($curso['prazo_calculado'])) {
        $hoje = new DateTime();
        $prazo = new DateTime($curso['prazo_calculado']);
        $diff = $hoje->diff($prazo);

        if ($prazo < $hoje) {
            $curso['status_prazo'] = 'vencido';
            $curso['dias_prazo'] = -$diff->days;
            $curso['classe_prazo'] = 'danger';
            $curso['texto_prazo'] = 'Vencido há ' . $diff->days . ' dias';
        } elseif ($diff->days <= 30) {
            $curso['status_prazo'] = 'a_vencer';
            $curso['dias_prazo'] = $diff->days;
            $curso['classe_prazo'] = 'warning';
            $curso['texto_prazo'] = 'Vence em ' . $diff->days . ' dias';
        } else {
            $curso['status_prazo'] = 'em_dia';
            $curso['dias_prazo'] = $diff->days;
            $curso['classe_prazo'] = 'success';
            $curso['texto_prazo'] = 'Vence em ' . $diff->days . ' dias';
        }
    } else {
        $curso['status_prazo'] = 'sem_prazo';
        $curso['dias_prazo'] = null;
        $curso['classe_prazo'] = 'secondary';
        $curso['texto_prazo'] = 'Sem prazo definido';
    }

    $cursos[] = $curso;
}

// Calcular métricas corretas baseadas nos prazos personalizados
$cursos_vencidos_corretos = 0;
$cursos_a_vencer_corretos = 0;
$cursos_concluidos_corretos = 0;

foreach ($cursos as $curso) {
    if ($curso['status_prazo'] === 'vencido') {
        $cursos_vencidos_corretos++;
    } elseif ($curso['status_prazo'] === 'a_vencer') {
        $cursos_a_vencer_corretos++;
    }

    // Contar cursos concluídos corretamente
    if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
        $cursos_concluidos_corretos++;
    }
}

// Atualizar as métricas do colaborador com os valores corretos
$colaborador['cursos_vencidos'] = $cursos_vencidos_corretos;
$colaborador['cursos_a_vencer'] = $cursos_a_vencer_corretos;
$colaborador['cursos_concluidos'] = $cursos_concluidos_corretos;

// Agrupar cursos por trilha
$cursos_por_trilha = [];
foreach ($cursos as $curso) {
    $cursos_por_trilha[$curso['trilha']][] = $curso;
}

// Função para formatar CPF
function formatarCpf($cpf) {
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

// Função para formatar data
function formatarData($data) {
    if (empty($data) || $data === '0000-00-00') return 'N/A';
    return date('d/m/Y', strtotime($data));
}

// Função para calcular prazo personalizado
function calcularPrazoPersonalizado($cpf, $codigo_trilha, $codigo_recurso, $data_admissao, $prazo_padrao, $pdo) {
    // Validar parâmetros de entrada
    if (empty($cpf) || empty($codigo_trilha) || empty($codigo_recurso) || empty($data_admissao)) {
        return $prazo_padrao;
    }

    // Validar data de admissão
    if ($data_admissao === '0000-00-00' || !strtotime($data_admissao)) {
        return $prazo_padrao;
    }

    // REGRA: Prazos personalizados só valem para colaboradores admitidos após 01/01/2023
    $data_corte = '2023-01-01';
    if ($data_admissao <= $data_corte) {
        return $prazo_padrao; // Usar prazo padrão para colaboradores antigos
    }

    // Buscar configuração do prazo personalizado
    $query_config = "
        SELECT primeiro_prazo_dias, renovacao_prazo_dias
        FROM edu_prazos_personalizados
        WHERE codigo_trilha = ? AND codigo_recurso = ? AND prazo_personalizado_ativo = 1
    ";

    $stmt_config = $pdo->prepare($query_config);
    $stmt_config->execute([$codigo_trilha, $codigo_recurso]);
    $config = $stmt_config->fetch(PDO::FETCH_ASSOC);

    if (!$config) {
        return $prazo_padrao; // Fallback para prazo padrão
    }

    // Validar se os valores de prazo são válidos
    $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
    $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

    if ($primeiro_prazo <= 0) {
        return $prazo_padrao; // Fallback se primeiro prazo inválido
    }

    // Verificar se já houve conclusões anteriores
    $query_conclusoes = "
        SELECT data_conclusao
        FROM edu_relatorio_educacao
        WHERE cpf = ? AND codigo_trilha = ? AND codigo_recurso = ?
        AND data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00'
        ORDER BY data_conclusao DESC
        LIMIT 1
    ";

    $stmt_conclusoes = $pdo->prepare($query_conclusoes);
    $stmt_conclusoes->execute([$cpf, $codigo_trilha, $codigo_recurso]);
    $ultima_conclusao = $stmt_conclusoes->fetch(PDO::FETCH_ASSOC);

    try {
        if ($ultima_conclusao) {
            // NOVA REGRA: Se renovação está vazia (0), curso não tem renovação
            if ($renovacao_prazo <= 0) {
                return null; // Curso concluído e sem renovação - sem prazo
            }

            // Renovação: usar data da última conclusão + prazo de renovação
            // Validar data de conclusão
            if ($ultima_conclusao['data_conclusao'] === '0000-00-00' || !strtotime($ultima_conclusao['data_conclusao'])) {
                return $prazo_padrao;
            }

            $data_base = new DateTime($ultima_conclusao['data_conclusao']);
            $data_base->add(new DateInterval('P' . $renovacao_prazo . 'D'));
        } else {
            // Primeira vez: usar data de admissão + primeiro prazo
            $data_base = new DateTime($data_admissao);
            $data_base->add(new DateInterval('P' . $primeiro_prazo . 'D'));
        }

        return $data_base->format('Y-m-d');

    } catch (Exception $e) {
        // Em caso de erro, retornar prazo padrão
        return $prazo_padrao;
    }
}

// Função para calcular status do curso
function getStatusCurso($curso) {
    if ($curso['aprovacao'] === 'Sim') {
        return ['classe' => 'success', 'texto' => 'Aprovado', 'icone' => 'fa-check-circle'];
    } elseif (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
        return ['classe' => 'warning', 'texto' => 'Concluído', 'icone' => 'fa-clock'];
    } else {
        return ['classe' => 'secondary', 'texto' => 'Pendente', 'icone' => 'fa-hourglass-half'];
    }
}

// Função para calcular status do colaborador (mesma do card principal)
function calcularStatusColaborador($colaborador) {
    global $pdo_edu, $prazos_config;

    $total_cursos = $colaborador['total_cursos'];
    $cursos_concluidos = $colaborador['cursos_concluidos'];
    $cursos_vencidos = $colaborador['cursos_vencidos'];
    $cursos_a_vencer = $colaborador['cursos_a_vencer'];

    // Verificar se há cursos em andamento
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    $tem_em_andamento = false;

    foreach ($cursos_colaborador as $curso) {
        if (!empty($curso['andamento_etapa']) &&
            $curso['aprovacao'] !== 'Sim' &&
            $curso['status_prazo'] !== 'vencido') {
            $tem_em_andamento = true;
            break;
        }
    }

    if ($cursos_vencidos > 0) {
        return ['status' => 'vencido', 'texto' => 'Cursos Vencidos', 'classe' => 'danger'];
    } elseif ($cursos_a_vencer > 0) {
        return ['status' => 'a_vencer', 'texto' => 'A Vencer', 'classe' => 'warning'];
    } elseif ($tem_em_andamento) {
        return ['status' => 'em_andamento', 'texto' => 'Em Andamento', 'classe' => 'sicoob-turquesa'];
    } elseif ($cursos_concluidos > 0) {
        return ['status' => 'em_dia', 'texto' => 'Em Dia', 'classe' => 'sicoob-verde-claro'];
    } else {
        return ['status' => 'sem_cursos', 'texto' => 'Sem Cursos', 'classe' => 'secondary'];
    }
}

// Função para buscar cursos do colaborador (necessária para calcular status)
function buscarCursosColaborador($cpf, $pdo, $prazos_config) {
    $query = "
        SELECT
            trilha,
            codigo_trilha,
            recurso,
            codigo_recurso,
            aprovacao,
            nota_recurso,
            aproveitamento,
            carga_horaria_recurso,
            data_conclusao,
            validade_recurso,
            andamento_etapa,
            concluir_trilha_ate,
            data_admissao
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        ORDER BY trilha, recurso
    ";

    $stmt = $pdo->prepare($query);
    $stmt->execute([$cpf]);
    $cursos_raw = $stmt->fetchAll();

    $cursos = [];
    foreach ($cursos_raw as $curso) {
        $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];

        if (isset($prazos_config[$key])) {
            $data_corte = '2023-01-01';
            $elegivel_prazo_personalizado = ($curso['data_admissao'] > $data_corte);

            if ($elegivel_prazo_personalizado) {
                $prazo_calculado = calcularPrazoPersonalizado(
                    $cpf,
                    $curso['codigo_trilha'],
                    $curso['codigo_recurso'],
                    $curso['data_admissao'],
                    $curso['concluir_trilha_ate'],
                    $pdo
                );

                if ($prazo_calculado === null) {
                    $curso['prazo_calculado'] = null;
                    $curso['prazo_personalizado'] = true;
                    $curso['motivo_prazo'] = 'Curso concluído (sem renovação)';
                } else {
                    $curso['prazo_calculado'] = $prazo_calculado;
                    $curso['prazo_personalizado'] = true;
                    $curso['motivo_prazo'] = 'Prazo personalizado aplicado';
                }
            } else {
                $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
                $curso['prazo_personalizado'] = false;
                $curso['motivo_prazo'] = 'Prazo padrão (admitido antes de 01/01/2023)';
            }
        } else {
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                $curso['prazo_calculado'] = null;
                $curso['prazo_personalizado'] = false;
                $curso['motivo_prazo'] = 'Curso concluído (sem renovação)';
            } else {
                $curso['prazo_calculado'] = $curso['concluir_trilha_ate'];
                $curso['prazo_personalizado'] = false;
                $curso['motivo_prazo'] = 'Prazo padrão (sem configuração personalizada)';
            }
        }

        // Calcular status do prazo
        if ($curso['prazo_calculado'] === null) {
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                $curso['status_prazo'] = 'concluido_sem_renovacao';
                $curso['dias_prazo'] = null;
                $curso['classe_prazo'] = 'success';
                $curso['texto_prazo'] = 'Concluído';
            } else {
                $curso['status_prazo'] = 'sem_prazo';
                $curso['dias_prazo'] = null;
                $curso['classe_prazo'] = 'secondary';
                $curso['texto_prazo'] = 'Sem prazo definido';
            }
        } elseif (!empty($curso['prazo_calculado'])) {
            $hoje = new DateTime();
            $prazo = new DateTime($curso['prazo_calculado']);
            $diff = $hoje->diff($prazo);

            if ($prazo < $hoje) {
                $curso['status_prazo'] = 'vencido';
                $curso['dias_prazo'] = -$diff->days;
                $curso['classe_prazo'] = 'danger';
                $curso['texto_prazo'] = 'Vencido há ' . $diff->days . ' dias';
            } elseif ($diff->days <= 30) {
                $curso['status_prazo'] = 'a_vencer';
                $curso['dias_prazo'] = $diff->days;
                $curso['classe_prazo'] = 'warning';
                $curso['texto_prazo'] = 'Vence em ' . $diff->days . ' dias';
            } else {
                $curso['status_prazo'] = 'em_dia';
                $curso['dias_prazo'] = $diff->days;
                $curso['classe_prazo'] = 'success';
                $curso['texto_prazo'] = 'Vence em ' . $diff->days . ' dias';
            }
        } else {
            $curso['status_prazo'] = 'sem_prazo';
            $curso['dias_prazo'] = null;
            $curso['classe_prazo'] = 'secondary';
            $curso['texto_prazo'] = 'Sem prazo definido';
        }

        $cursos[] = $curso;
    }

    return $cursos;
}
?>

<div class="container-fluid">
    <!-- Informações do Colaborador -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Informações do Colaborador</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center mb-3">
                            <!-- Foto do Colaborador -->
                            <?php if ($usuario_intranet && !empty($usuario_intranet['foto_url'])): ?>
                                <img src="<?php echo htmlspecialchars($usuario_intranet['foto_url']); ?>"
                                     alt="Foto do colaborador"
                                     class="rounded-circle mb-2"
                                     style="width: 120px; height: 120px; object-fit: cover; border: 3px solid #e9ecef;"
                                     onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                <div class="rounded-circle bg-light d-none align-items-center justify-content-center mb-2"
                                     style="width: 120px; height: 120px; border: 3px solid #e9ecef; margin: 0 auto;">
                                    <i class="fas fa-user text-muted" style="font-size: 3rem;"></i>
                                </div>
                            <?php else: ?>
                                <div class="rounded-circle bg-light d-flex align-items-center justify-content-center mb-2"
                                     style="width: 120px; height: 120px; border: 3px solid #e9ecef; margin: 0 auto;">
                                    <i class="fas fa-user text-muted" style="font-size: 3rem;"></i>
                                </div>
                            <?php endif; ?>
                            <h6 class="mb-0"><?php echo htmlspecialchars($usuario_intranet['nome'] ?? $colaborador['usuario']); ?></h6>
                            <small class="text-muted"><?php echo formatarCpf($colaborador['cpf']); ?></small>
                        </div>
                        <div class="col-md-4">
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="py-2 border-bottom">
                                        <strong class="text-muted d-block mb-1">Email</strong>
                                        <span class="text-dark"><?php echo htmlspecialchars($usuario_intranet['email'] ?? $colaborador['email'] ?: 'N/A'); ?></span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="py-2 border-bottom">
                                        <strong class="text-muted d-block mb-1">Data Admissão</strong>
                                        <span class="text-dark"><?php echo formatarData($colaborador['data_admissao']); ?></span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="py-2">
                                        <strong class="text-muted d-block mb-1">Função/Cargo</strong>
                                        <span class="text-dark"><?php echo htmlspecialchars($usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?: 'N/A'); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-5">
                            <div class="row g-2">
                                <div class="col-12">
                                    <div class="py-2 border-bottom">
                                        <strong class="text-muted d-block mb-1">Agência</strong>
                                        <span class="text-dark">
                                            <?php
                                            // Buscar informações da agência no formato "número - nome"
                                            $agencia_info = 'N/A';
                                            if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
                                                $agencia_id = $usuario_intranet['agencia'];
                                                if (isset($mapa_agencias[$agencia_id])) {
                                                    $agencia_data = $mapa_agencias[$agencia_id];
                                                    $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
                                                } else {
                                                    $agencia_info = $agencia_id; // Fallback para ID se não encontrar
                                                }
                                            } elseif (!empty($colaborador['codigo_unidade'])) {
                                                $agencia_info = $colaborador['codigo_unidade']; // Fallback para código da unidade
                                            }
                                            echo htmlspecialchars($agencia_info);
                                            ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="py-2 border-bottom">
                                        <strong class="text-muted d-block mb-1">Setor</strong>
                                        <span class="text-dark"><?php echo htmlspecialchars($usuario_intranet['nomeSetor'] ?? 'N/A'); ?></span>
                                    </div>
                                </div>
                                <div class="col-12">
                                    <div class="py-2 border-bottom">
                                        <strong class="text-muted d-block mb-1">Status Educação</strong>
                                        <span class="text-dark">
                                            <?php
                                            // Calcular status do colaborador (mesmo do card principal)
                                            $status_colaborador = calcularStatusColaborador($colaborador);
                                            ?>
                                            <span class="badge bg-<?php echo $status_colaborador['classe']; ?>">
                                                <?php if ($status_colaborador['status'] === 'vencido'): ?>
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                <?php elseif ($status_colaborador['status'] === 'a_vencer'): ?>
                                                    <i class="fas fa-clock me-1"></i>
                                                <?php elseif ($status_colaborador['status'] === 'em_andamento'): ?>
                                                    <i class="fas fa-play me-1"></i>
                                                <?php elseif ($status_colaborador['status'] === 'em_dia'): ?>
                                                    <i class="fas fa-check me-1"></i>
                                                <?php else: ?>
                                                    <i class="fas fa-minus me-1"></i>
                                                <?php endif; ?>
                                                <?php echo $status_colaborador['texto']; ?>
                                            </span>
                                        </span>
                                    </div>
                                </div>
                                <?php if ($usuario_intranet && isset($usuario_intranet['status_bloqueio'])): ?>
                                <div class="col-12">
                                    <div class="py-2">
                                        <strong class="text-muted d-block mb-1">Status Intranet</strong>
                                        <span class="text-dark">
                                            <span class="badge bg-<?php echo $usuario_intranet['status_bloqueio']['classe']; ?>"
                                                  title="<?php echo htmlspecialchars($usuario_intranet['status_bloqueio']['descricao']); ?>">
                                                <i class="fas <?php echo $usuario_intranet['status_bloqueio']['icone']; ?> me-1"></i>
                                                <?php echo htmlspecialchars($usuario_intranet['status_bloqueio']['texto']); ?>
                                            </span>
                                        </span>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Botão Enviar Email -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="d-flex justify-content-end">
                                <?php
                                $tem_email = !empty($usuario_intranet['email']) || !empty($colaborador['email']);
                                ?>
                                <button type="button" class="btn <?php echo $tem_email ? '' : 'disabled'; ?>"
                                        onclick="abrirModalEnvioEmail()"
                                        <?php if ($tem_email): ?>
                                        style="background: linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro));
                                               color: white; border: none; box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                                        onmouseover="this.style.background='linear-gradient(135deg, var(--sicoob-verde-escuro), var(--sicoob-turquesa))'"
                                        onmouseout="this.style.background='linear-gradient(135deg, var(--sicoob-turquesa), var(--sicoob-verde-escuro))'"
                                        <?php else: ?>
                                        style="background: #6c757d; color: white; border: none; cursor: not-allowed;"
                                        title="Colaborador não possui email cadastrado"
                                        disabled
                                        <?php endif; ?>>
                                    <i class="fas fa-envelope me-2"></i>Enviar Email
                                    <?php if (!$tem_email): ?>
                                    <i class="fas fa-exclamation-triangle ms-1" title="Email não disponível"></i>
                                    <?php endif; ?>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    <!-- Estatísticas Resumidas -->
    <div class="row mb-4">
        <div class="col-xl col-lg-3 col-md-4 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body py-3">
                    <h4 class="text-primary mb-1"><?php echo $colaborador['total_trilhas']; ?></h4>
                    <small class="text-muted">Trilhas</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-3 col-md-4 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body py-3">
                    <h4 class="text-info mb-1"><?php echo $colaborador['total_cursos']; ?></h4>
                    <small class="text-muted">Cursos</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-3 col-md-4 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body py-3">
                    <h4 class="text-success mb-1"><?php echo $colaborador['cursos_aprovados']; ?></h4>
                    <small class="text-muted">Aprovados</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-3 col-md-4 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body py-3">
                    <h4 class="text-info mb-1"><?php echo $colaborador['cursos_concluidos']; ?></h4>
                    <small class="text-muted">Concluídos</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-3 col-md-4 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body py-3">
                    <h4 class="text-warning mb-1"><?php echo $colaborador['cursos_a_vencer']; ?></h4>
                    <small class="text-muted">A Vencer</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-3 col-md-4 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body py-3">
                    <h4 class="text-danger mb-1"><?php echo $colaborador['cursos_vencidos']; ?></h4>
                    <small class="text-muted">Vencidos</small>
                </div>
            </div>
        </div>
        <div class="col-xl col-lg-3 col-md-4 col-6 mb-3">
            <div class="card text-center">
                <div class="card-body py-3">
                    <h4 class="text-secondary mb-1"><?php echo number_format($colaborador['media_aproveitamento'] ?? 0, 1); ?>%</h4>
                    <small class="text-muted">Aproveitamento</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Trilhas -->
    <div class="card mb-4">
        <div class="card-header">
            <h6 class="mb-0"><i class="fas fa-route me-2"></i>Trilhas de Aprendizagem</h6>
        </div>
        <div class="card-body">
            <?php if (empty($trilhas)): ?>
            <p class="text-muted">Nenhuma trilha encontrada.</p>
            <?php else: ?>
            <div class="accordion" id="trilhasAccordion">
                <?php foreach ($trilhas as $index => $trilha): ?>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="heading<?php echo $index; ?>">
                        <button class="accordion-button <?php echo $index > 0 ? 'collapsed' : ''; ?>" 
                                type="button" data-bs-toggle="collapse" 
                                data-bs-target="#collapse<?php echo $index; ?>">
                            <div class="d-flex justify-content-between align-items-center w-100 me-3">
                                <div>
                                    <div class="d-flex align-items-center gap-2 mb-1">
                                        <strong><?php echo htmlspecialchars($trilha['trilha']); ?></strong>
                                        <?php
                                        // Verificar se a trilha é obrigatória
                                        $trilha_obrigatoria = isset($trilhas_obrigatorias[$trilha['codigo_trilha']]);
                                        if ($trilha_obrigatoria):
                                        ?>
                                        <span class="badge bg-warning text-dark">
                                            <i class="fas fa-exclamation-triangle me-1"></i>OBRIGATÓRIA
                                        </span>
                                        <?php endif; ?>
                                    </div>
                                    <small class="text-muted">
                                        <?php echo $trilha['total_cursos_trilha']; ?> cursos •
                                        <?php echo $trilha['cursos_aprovados_trilha']; ?> aprovados
                                    </small>
                                </div>
                                <div class="d-flex align-items-center gap-2">
                                    <?php
                                    // Verificar se há cursos vencidos e a vencer nesta trilha
                                    $tem_cursos_vencidos = false;
                                    $tem_cursos_a_vencer = false;
                                    if (isset($cursos_por_trilha[$trilha['trilha']])) {
                                        foreach ($cursos_por_trilha[$trilha['trilha']] as $curso) {
                                            if ($curso['status_prazo'] === 'vencido') {
                                                $tem_cursos_vencidos = true;
                                            } elseif ($curso['status_prazo'] === 'a_vencer') {
                                                $tem_cursos_a_vencer = true;
                                            }
                                        }
                                    }
                                    ?>

                                    <?php if ($tem_cursos_vencidos): ?>
                                        <i class="fas fa-exclamation-triangle text-danger"
                                           title="Esta trilha possui cursos vencidos"
                                           data-bs-toggle="tooltip"></i>
                                    <?php endif; ?>

                                    <?php if ($tem_cursos_a_vencer): ?>
                                        <i class="fas fa-clock text-warning"
                                           title="Esta trilha possui cursos a vencer"
                                           data-bs-toggle="tooltip"></i>
                                    <?php endif; ?>

                                    <?php if ($trilha['situacao_trilha'] === 'Concluída'): ?>
                                    <span class="badge bg-success">Concluída</span>
                                    <?php elseif ($trilha['aprovado_trilha'] === 'Sim'): ?>
                                    <span class="badge bg-sicoob-verde-claro">Aprovada</span>
                                    <?php else: ?>
                                    <span class="badge bg-secondary">Em Andamento</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </button>
                    </h2>
                    <div id="collapse<?php echo $index; ?>" 
                         class="accordion-collapse collapse <?php echo $index === 0 ? 'show' : ''; ?>" 
                         data-bs-parent="#trilhasAccordion">
                        <div class="accordion-body">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <small><strong>Código:</strong> <?php echo htmlspecialchars($trilha['codigo_trilha']); ?></small><br>
                                    <small><strong>Situação:</strong> <?php echo htmlspecialchars($trilha['situacao_trilha'] ?: 'N/A'); ?></small><br>
                                    <small><strong>Aproveitamento:</strong> <?php echo number_format($trilha['aproveitamento'] ?? 0, 1); ?>%</small><br>
                                    <?php
                                    // Verificar se a trilha é obrigatória
                                    $trilha_obrigatoria = isset($trilhas_obrigatorias[$trilha['codigo_trilha']]);
                                    ?>
                                    <small><strong>Tipo:</strong>
                                        <?php if ($trilha_obrigatoria): ?>
                                            <span class="text-warning fw-bold">
                                                <i class="fas fa-exclamation-triangle me-1"></i>Trilha Obrigatória
                                            </span>
                                        <?php else: ?>
                                            <span class="text-muted">Trilha Opcional</span>
                                        <?php endif; ?>
                                    </small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>Iniciar em:</strong> <?php echo formatarData($trilha['iniciar_trilha_em']); ?></small><br>
                                    <small><strong>Concluir até:</strong> <?php echo formatarData($trilha['concluir_trilha_ate']); ?></small><br>
                                    <small><strong>Data Aprovação:</strong> <?php echo formatarData($trilha['data_aprovacao_trilha']); ?></small>
                                </div>
                            </div>
                            
                            <!-- Cursos da Trilha -->
                            <?php if (isset($cursos_por_trilha[$trilha['trilha']])): ?>
                            <h6>Cursos desta Trilha:</h6>
                            <div class="table-responsive">
                                <table class="table table-sm">
                                    <thead>
                                        <tr>
                                            <th>Curso</th>
                                            <th>Status</th>
                                            <th>Nota</th>
                                            <th>Aproveitamento</th>
                                            <th>Conclusão</th>
                                            <th>Prazo</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($cursos_por_trilha[$trilha['trilha']] as $curso): ?>
                                        <?php $status = getStatusCurso($curso); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($curso['recurso']); ?></strong><br>
                                                <small class="text-muted"><?php echo htmlspecialchars($curso['codigo_recurso']); ?></small>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $status['classe']; ?>">
                                                    <i class="fas <?php echo $status['icone']; ?> me-1"></i>
                                                    <?php echo $status['texto']; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $curso['nota_recurso'] ? number_format($curso['nota_recurso'], 1) : 'N/A'; ?></td>
                                            <td><?php echo $curso['aproveitamento'] ? number_format($curso['aproveitamento'], 1) . '%' : 'N/A'; ?></td>
                                            <td><?php echo formatarData($curso['data_conclusao']); ?></td>
                                            <td>
                                                <?php if ($curso['prazo_calculado'] === null): ?>
                                                    <div>
                                                        <span class="badge bg-<?php echo $curso['classe_prazo']; ?> badge-sm">
                                                            <?php echo $curso['texto_prazo']; ?>
                                                        </span>
                                                        <?php if ($curso['prazo_personalizado']): ?>
                                                            <br><small class="text-success"><i class="fas fa-cog me-1"></i>Personalizado</small>
                                                        <?php else: ?>
                                                            <br><small class="text-muted"><i class="fas fa-clock me-1"></i>Padrão</small>
                                                        <?php endif; ?>
                                                        <?php if (isset($curso['motivo_prazo'])): ?>
                                                            <br><small class="text-muted" style="font-size: 0.7rem;" title="<?php echo htmlspecialchars($curso['motivo_prazo']); ?>">
                                                                <i class="fas fa-info-circle me-1"></i><?php echo strlen($curso['motivo_prazo']) > 30 ? substr($curso['motivo_prazo'], 0, 30) . '...' : $curso['motivo_prazo']; ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php elseif (!empty($curso['prazo_calculado'])): ?>
                                                    <div>
                                                        <small><strong><?php echo formatarData($curso['prazo_calculado']); ?></strong></small>
                                                        <?php if ($curso['prazo_personalizado']): ?>
                                                            <br><small class="text-success"><i class="fas fa-cog me-1"></i>Personalizado</small>
                                                        <?php else: ?>
                                                            <br><small class="text-muted"><i class="fas fa-clock me-1"></i>Padrão</small>
                                                        <?php endif; ?>
                                                        <br><span class="badge bg-<?php echo $curso['classe_prazo']; ?> badge-sm">
                                                            <?php echo $curso['texto_prazo']; ?>
                                                        </span>
                                                        <?php if (isset($curso['motivo_prazo'])): ?>
                                                            <br><small class="text-muted" style="font-size: 0.7rem;" title="<?php echo htmlspecialchars($curso['motivo_prazo']); ?>">
                                                                <i class="fas fa-info-circle me-1"></i><?php echo strlen($curso['motivo_prazo']) > 30 ? substr($curso['motivo_prazo'], 0, 30) . '...' : $curso['motivo_prazo']; ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">Sem prazo</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>


