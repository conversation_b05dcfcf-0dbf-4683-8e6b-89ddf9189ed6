<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';
require_once 'classes/IntranetAPI.php';

header('Content-Type: application/json');

try {
    $cpf = $_GET['cpf'] ?? null;
    
    if (!$cpf) {
        throw new Exception("CPF é obrigatório");
    }
    
    // Normalizar CPF
    $cpf = str_pad(preg_replace('/[^0-9]/', '', $cpf), 11, '0', STR_PAD_LEFT);
    
    // Buscar dados do colaborador na base de educação
    $stmt = $pdo_edu->prepare("
        SELECT
            cpf,
            usuario,
            email,
            funcao,
            codigo_unidade,
            hierarquia_unidade,
            data_admissao,
            superior_imediato
        FROM edu_relatorio_educacao
        WHERE cpf = ? AND funcao != 'APRENDIZ'
        GROUP BY cpf, usuario, email, funcao, codigo_unidade, hierarquia_unidade, data_admissao, superior_imediato
        LIMIT 1
    ");
    $stmt->execute([$cpf]);
    $colaborador = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$colaborador) {
        throw new Exception("Colaborador não encontrado ou possui função APRENDIZ");
    }
    
    // Buscar dados da Intranet - APENAS USUÁRIOS ATIVOS
    $api = new IntranetAPI();
    $usuario_intranet = null;
    $usuarios_intranet = $api->listarUsuariosAtivos();
    if ($usuarios_intranet !== false) {
        foreach ($usuarios_intranet as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_intranet = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                if ($cpf_intranet === $cpf) {
                    $usuario_intranet = $usuario;
                    break;
                }
            }
        }
    }
    
    // Buscar agências da Intranet
    $agencias_intranet = $api->listarAgencias();
    $mapa_agencias = [];
    if ($agencias_intranet !== false) {
        foreach ($agencias_intranet as $agencia) {
            if (!empty($agencia['id'])) {
                $mapa_agencias[$agencia['id']] = $agencia;
            }
        }
    }
    
    // Preparar dados do colaborador
    $dados_colaborador = [
        'cpf' => $colaborador['cpf'],
        'nome' => $usuario_intranet['nome'] ?? $colaborador['usuario'],
        'email' => $usuario_intranet['email'] ?? $colaborador['email'],
        'funcao' => $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'],
        'pa' => $colaborador['codigo_unidade']
    ];
    
    // Buscar informações da agência
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $dados_colaborador['pa'] = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        }
    }
    
    echo json_encode([
        'success' => true,
        'colaborador' => $dados_colaborador
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
