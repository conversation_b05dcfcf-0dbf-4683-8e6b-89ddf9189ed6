<?php
// Relatório Completo de Cursos - VERSÃO OTIMIZADA
// Este arquivo gera um relatório detalhado de todos os cursos com seus participantes

// Aumentar limite de tempo para relatórios grandes
set_time_limit(300); // 5 minutos

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['curso'])) {
    $where_conditions[] = "recurso LIKE ?";
    $params[] = '%' . $filtros['curso'] . '%';
}

// Query otimizada para buscar cursos com estatísticas básicas
$cursos_query = "
    SELECT
        codigo_recurso,
        recurso,
        trilha,
        codigo_trilha,
        carga_horaria_recurso,
        COUNT(DISTINCT cpf) as total_colaboradores,
        SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
        COUNT(*) as total_registros,
        AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas,
        AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
        COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    WHERE funcao != 'APRENDIZ'" . (!empty($where_conditions) ? " AND " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY codigo_recurso, recurso, trilha, codigo_trilha, carga_horaria_recurso
    ORDER BY trilha, recurso
    LIMIT 50
";

$stmt_cursos = $pdo_edu->prepare($cursos_query);
$stmt_cursos->execute($params);
$cursos = $stmt_cursos->fetchAll();

// Query otimizada para buscar participantes de todos os cursos de uma vez
$participantes_query = "
    SELECT
        codigo_recurso, recurso, cpf, usuario, email, funcao, aprovacao,
        nota_recurso, aproveitamento, data_conclusao, andamento_etapa,
        concluir_trilha_ate, data_admissao
    FROM edu_relatorio_educacao
    WHERE funcao != 'APRENDIZ'" . (!empty($where_conditions) ? " AND " . implode(" AND ", $where_conditions) : "") . "
    ORDER BY codigo_recurso, recurso, usuario
";

$stmt_participantes = $pdo_edu->prepare($participantes_query);
$stmt_participantes->execute($params);
$todos_participantes = $stmt_participantes->fetchAll();

// Agrupar participantes por curso
$participantes_por_curso = [];
foreach ($todos_participantes as $participante) {
    $key = $participante['codigo_recurso'] . '|' . $participante['recurso'];
    if (!isset($participantes_por_curso[$key])) {
        $participantes_por_curso[$key] = [];
    }
    $participantes_por_curso[$key][] = $participante;
}

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #003641; color: white; font-weight: bold;">';
echo '<td colspan="19" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO COMPLETO DE CURSOS - EDUCAÇÃO CORPORATIVA';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="18" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

if (!empty($filtros['trilha']) || !empty($filtros['curso'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Aplicados:</td>';
    echo '<td colspan="18" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['curso'])) $filtros_texto[] = "Curso: " . $filtros['curso'];
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Cursos:</td>';
echo '<td colspan="18" style="padding: 8px;">' . count($cursos) . ' (limitado a 50 para performance)</td>';
echo '</tr>';

$total_participantes = array_sum(array_column($cursos, 'total_colaboradores'));
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Participantes:</td>';
echo '<td colspan="18" style="padding: 8px;">' . $total_participantes . '</td>';
echo '</tr>';

echo '<tr><td colspan="19" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos das colunas
echo '<tr style="background-color: #00AE9D; color: white; font-weight: bold;">';
echo '<td style="padding: 8px; text-align: center;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha Obrigatória</td>';
echo '<td style="padding: 8px; text-align: center;">Curso</td>';
echo '<td style="padding: 8px; text-align: center;">Código</td>';
echo '<td style="padding: 8px; text-align: center;">Carga Horária</td>';
echo '<td style="padding: 8px; text-align: center;">Total Participantes</td>';
echo '<td style="padding: 8px; text-align: center;">Aprovados</td>';
echo '<td style="padding: 8px; text-align: center;">Concluídos</td>';
echo '<td style="padding: 8px; text-align: center;">% Aprovação</td>';
echo '<td style="padding: 8px; text-align: center;">Média Notas</td>';
echo '<td style="padding: 8px; text-align: center;">Média Aproveitamento</td>';
echo '<td style="padding: 8px; text-align: center;">Vencidos</td>';
echo '<td style="padding: 8px; text-align: center;">A Vencer</td>';
echo '<td style="padding: 8px; text-align: center;">Em Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">Status Geral</td>';
echo '<td style="padding: 8px; text-align: center;">CPF Participante</td>';
echo '<td style="padding: 8px; text-align: center;">Nome Participante</td>';
echo '<td style="padding: 8px; text-align: center;">Status Individual</td>';
echo '<td style="padding: 8px; text-align: center;">Prazo</td>';
echo '</tr>';

// Dados dos cursos
$linha = 0;
foreach ($cursos as $curso) {
    $linha++;

    // Buscar participantes do curso do array pré-carregado
    $key = $curso['codigo_recurso'] . '|' . $curso['recurso'];
    $participantes = isset($participantes_por_curso[$key]) ? $participantes_por_curso[$key] : [];

    // Calcular estatísticas simplificadas (sem cálculo de prazos complexos para performance)
    $vencidos = 0;
    $a_vencer = 0;
    $em_andamento = 0;

    $participantes_detalhados = [];
    foreach ($participantes as $participante) {
        // Cálculo simplificado de status baseado apenas no prazo padrão
        $prazo_calculado = $participante['concluir_trilha_ate'];
        $status_prazo = 'sem_prazo';
        $dias_prazo = null;

        if (!empty($prazo_calculado) && $prazo_calculado !== '0000-00-00') {
            $hoje = new DateTime();
            $prazo = new DateTime($prazo_calculado);
            $diff = $hoje->diff($prazo);

            if ($prazo < $hoje) {
                $status_prazo = 'vencido';
                $dias_prazo = -$diff->days;
                $vencidos++;
            } elseif ($diff->days <= 30) {
                $status_prazo = 'a_vencer';
                $dias_prazo = $diff->days;
                $a_vencer++;
            } else {
                $status_prazo = 'em_dia';
                $dias_prazo = $diff->days;
            }
        }

        if (!empty($participante['andamento_etapa']) && $participante['aprovacao'] !== 'Sim') {
            $em_andamento++;
        }

        $participante['status_prazo'] = $status_prazo;
        $participante['dias_prazo'] = $dias_prazo;
        $participante['prazo_calculado'] = $prazo_calculado;

        $participantes_detalhados[] = $participante;
    }
    
    // Determinar status geral do curso
    $percentual_aprovacao = $curso['total_colaboradores'] > 0 ? 
        ($curso['total_aprovados'] / $curso['total_colaboradores']) * 100 : 0;
    
    $status_geral = 'Excelente';
    $cor_status = '#28a745';
    if ($percentual_aprovacao < 40) {
        $status_geral = 'Crítico';
        $cor_status = '#dc3545';
    } elseif ($percentual_aprovacao < 60) {
        $status_geral = 'Regular';
        $cor_status = '#fd7e14';
    } elseif ($percentual_aprovacao < 80) {
        $status_geral = 'Bom';
        $cor_status = '#007bff';
    }
    
    // Filtrar por status se especificado
    if (!empty($filtros['status'])) {
        $tem_status_filtrado = false;
        foreach ($participantes_detalhados as $p) {
            $status_individual = '';
            if ($p['aprovacao'] === 'Sim') {
                $status_individual = 'aprovado';
            } elseif ($p['status_prazo'] === 'vencido') {
                $status_individual = 'vencido';
            } elseif ($p['status_prazo'] === 'a_vencer') {
                $status_individual = 'a_vencer';
            } elseif (!empty($p['andamento_etapa']) && $p['aprovacao'] !== 'Sim') {
                $status_individual = 'em_andamento';
            }
            
            if ($status_individual === $filtros['status']) {
                $tem_status_filtrado = true;
                break;
            }
        }
        
        if (!$tem_status_filtrado) continue;
    }
    
    // Se há participantes, criar uma linha para cada participante
    if (!empty($participantes_detalhados)) {
        foreach ($participantes_detalhados as $participante) {
            // Filtrar por status específico se necessário
            if (!empty($filtros['status'])) {
                $status_individual = '';
                if ($participante['aprovacao'] === 'Sim') {
                    $status_individual = 'aprovado';
                } elseif ($participante['status_prazo'] === 'vencido') {
                    $status_individual = 'vencido';
                } elseif ($participante['status_prazo'] === 'a_vencer') {
                    $status_individual = 'a_vencer';
                } elseif (!empty($participante['andamento_etapa']) && $participante['aprovacao'] !== 'Sim') {
                    $status_individual = 'em_andamento';
                }
                
                if ($status_individual !== $filtros['status']) continue;
            }
            
            $cor_linha = ($linha % 2 == 0) ? '#f8f9fa' : '#ffffff';
            echo '<tr style="background-color: ' . $cor_linha . ';">';
            
            // Repetir dados do curso em todas as linhas para compatibilidade com Excel
            echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';

            // Verificar se a trilha é obrigatória
            $trilha_obrigatoria = isTrilhaObrigatoria($curso['codigo_trilha'], $trilhas_obrigatorias);
            echo '<td style="padding: 6px; text-align: center;">';
            if ($trilha_obrigatoria) {
                echo '<span style="color: #856404; font-weight: bold;">SIM</span>';
            } else {
                echo '<span style="color: #6c757d;">NÃO</span>';
            }
            echo '</td>';

            echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($curso['codigo_recurso']) . '</td>';
            echo '<td style="padding: 6px; text-align: center;">' . ($curso['carga_horaria_recurso'] ?? 'N/A') . '</td>';
            echo '<td style="padding: 6px; text-align: center;">' . $curso['total_colaboradores'] . '</td>';
            echo '<td style="padding: 6px; text-align: center;">' . $curso['total_aprovados'] . '</td>';
            echo '<td style="padding: 6px; text-align: center;">' . $curso['total_concluidos'] . '</td>';
            echo '<td style="padding: 6px; text-align: center;">' . number_format($percentual_aprovacao, 1) . '%</td>';
            echo '<td style="padding: 6px; text-align: center;">' . ($curso['media_notas'] ? number_format($curso['media_notas'], 1) : 'N/A') . '</td>';
            echo '<td style="padding: 6px; text-align: center;">' . ($curso['media_aproveitamento'] ? number_format($curso['media_aproveitamento'], 1) . '%' : 'N/A') . '</td>';
            echo '<td style="padding: 6px; text-align: center; color: #dc3545;">' . $vencidos . '</td>';
            echo '<td style="padding: 6px; text-align: center; color: #fd7e14;">' . $a_vencer . '</td>';
            echo '<td style="padding: 6px; text-align: center; color: #007bff;">' . $em_andamento . '</td>';
            echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: ' . $cor_status . ';">' . $status_geral . '</td>';
            
            // Dados do participante
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $participante['cpf']), 11, '0', STR_PAD_LEFT);
            $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
            $nome_exibir = $usuario_intranet['nome'] ?? $participante['usuario'];
            
            echo '<td style="padding: 6px;">' . formatarCpf($participante['cpf']) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
            
            // Status individual
            $status_texto = 'Pendente';
            $cor_status_individual = '#6c757d';
            
            if ($participante['aprovacao'] === 'Sim') {
                $status_texto = 'Aprovado';
                $cor_status_individual = '#28a745';
                if ($participante['nota_recurso']) {
                    $status_texto .= ' (Nota: ' . $participante['nota_recurso'] . ')';
                }
            } elseif ($participante['status_prazo'] === 'vencido') {
                $status_texto = 'Vencido (' . abs($participante['dias_prazo']) . ' dias)';
                $cor_status_individual = '#dc3545';
            } elseif ($participante['status_prazo'] === 'a_vencer') {
                $status_texto = 'A Vencer (' . $participante['dias_prazo'] . ' dias)';
                $cor_status_individual = '#fd7e14';
            } elseif (!empty($participante['andamento_etapa'])) {
                $status_texto = 'Em Andamento: ' . $participante['andamento_etapa'];
                $cor_status_individual = '#007bff';
            }
            
            echo '<td style="padding: 6px; color: ' . $cor_status_individual . '; font-weight: bold;">' . $status_texto . '</td>';
            
            // Prazo
            echo '<td style="padding: 6px; text-align: center;">' . 
                 ($participante['prazo_calculado'] ? date('d/m/Y', strtotime($participante['prazo_calculado'])) : 'N/A') . '</td>';
            echo '</tr>';
        }
    } else {
        // Curso sem participantes
        $cor_linha = ($linha % 2 == 0) ? '#f8f9fa' : '#ffffff';
        echo '<tr style="background-color: ' . $cor_linha . ';">';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';

        // Verificar se a trilha é obrigatória
        $trilha_obrigatoria = isTrilhaObrigatoria($curso['codigo_trilha'], $trilhas_obrigatorias);
        echo '<td style="padding: 6px; text-align: center;">';
        if ($trilha_obrigatoria) {
            echo '<span style="color: #856404; font-weight: bold;">SIM</span>';
        } else {
            echo '<span style="color: #6c757d;">NÃO</span>';
        }
        echo '</td>';

        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['codigo_recurso']) . '</td>';
        echo '<td style="padding: 6px; text-align: center;">' . ($curso['carga_horaria_recurso'] ?? 'N/A') . '</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0%</td>';
        echo '<td style="padding: 6px; text-align: center;">N/A</td>';
        echo '<td style="padding: 6px; text-align: center;">N/A</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center;">0</td>';
        echo '<td style="padding: 6px; text-align: center; color: #6c757d;">Sem Participantes</td>';
        echo '<td style="padding: 6px; color: #999;">-</td>';
        echo '<td style="padding: 6px; color: #999;">Nenhum participante</td>';
        echo '<td style="padding: 6px; color: #999;">-</td>';
        echo '<td style="padding: 6px; color: #999;">-</td>';
        echo '</tr>';
    }
}

echo '</table>';
?>
