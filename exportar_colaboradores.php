<?php
require_once 'edu_auth_check.php';
require_once 'config/database.php';
require_once 'classes/IntranetAPI.php';

// Verificar permissões
checkEduPageAccess(['comum', 'gestor', 'admin']);

// Verificar se é uma requisição de exportação
if (!isset($_GET['export']) || $_GET['export'] !== 'excel') {
    header('Location: analise_colaboradores.php');
    exit;
}

// Inicializar API da Intranet
$api = new IntranetAPI();

// Tipo de exportação e filtros
$tipo_exportacao = $_GET['tipo'] ?? 'colaboradores';
$filtros = [
    'cpf' => $_GET['cpf'] ?? '',
    'nome' => $_GET['nome'] ?? '',
    'trilha' => $_GET['trilha'] ?? '',
    'situacao_trilha' => $_GET['situacao_trilha'] ?? '',
    'agencia' => $_GET['agencia'] ?? '',
    'funcao' => $_GET['funcao'] ?? '',
    'data_inicio' => $_GET['data_inicio'] ?? '',
    'data_fim' => $_GET['data_fim'] ?? '',
    'curso' => $_GET['curso'] ?? '',
    'aprovacao' => $_GET['aprovacao'] ?? ''
];

// Buscar dados da API da Intranet - APENAS USUÁRIOS ATIVOS
$usuarios_intranet = $api->listarUsuariosAtivos();
$mapa_usuarios_cpf = [];
if ($usuarios_intranet !== false) {
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $mapa_usuarios_cpf[$cpf_normalizado] = $usuario;
        }
    }
}

// Construir query com filtros
$where_conditions = [];
$params = [];

if (!empty($filtros['cpf'])) {
    $cpf_filtro = str_pad(preg_replace('/[^0-9]/', '', $filtros['cpf']), 11, '0', STR_PAD_LEFT);
    $where_conditions[] = "cpf = ?";
    $params[] = $cpf_filtro;
}

if (!empty($filtros['nome'])) {
    $where_conditions[] = "usuario LIKE ?";
    $params[] = '%' . $filtros['nome'] . '%';
}

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['situacao_trilha'])) {
    $where_conditions[] = "situacao_trilha = ?";
    $params[] = $filtros['situacao_trilha'];
}

if (!empty($filtros['data_inicio'])) {
    $where_conditions[] = "data_importacao >= ?";
    $params[] = $filtros['data_inicio'] . ' 00:00:00';
}

if (!empty($filtros['data_fim'])) {
    $where_conditions[] = "data_importacao <= ?";
    $params[] = $filtros['data_fim'] . ' 23:59:59';
}

if (!empty($filtros['curso'])) {
    $where_conditions[] = "recurso LIKE ?";
    $params[] = '%' . $filtros['curso'] . '%';
}

if (!empty($filtros['aprovacao'])) {
    $where_conditions[] = "aprovacao = ?";
    $params[] = $filtros['aprovacao'];
}

// FILTRO CRÍTICO: Adicionar filtro de usuários ativos da Intranet
$cpfs_ativos = [];
if ($usuarios_intranet !== false) {
    foreach ($usuarios_intranet as $usuario) {
        if (!empty($usuario['cpf'])) {
            $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
            $cpfs_ativos[] = $cpf_normalizado;
        }
    }
}

// Adicionar condição de CPFs ativos se houver usuários ativos
if (!empty($cpfs_ativos)) {
    $cpfs_placeholders = str_repeat('?,', count($cpfs_ativos) - 1) . '?';
    $where_conditions[] = "cpf IN ($cpfs_placeholders)";
    $params = array_merge($params, $cpfs_ativos);
}

// Definir dados baseado no tipo de exportação
$dados = [];
$filename_prefix = '';

if ($tipo_exportacao === 'cursos') {
    // Query para buscar todos os cursos (sem limite)
    $cursos_query = "
        SELECT
            codigo_recurso,
            recurso,
            trilha,
            codigo_trilha,
            carga_horaria_recurso,
            COUNT(DISTINCT cpf) as total_colaboradores,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as total_aprovados,
            COUNT(*) as total_registros,
            AVG(CASE WHEN nota_recurso > 0 THEN nota_recurso ELSE NULL END) as media_notas,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
            COUNT(CASE WHEN data_conclusao IS NOT NULL AND data_conclusao != '0000-00-00' THEN 1 END) as total_concluidos,
            MAX(data_importacao) as ultima_atualizacao
        FROM edu_relatorio_educacao
        " . (!empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "") . "
        GROUP BY codigo_recurso, recurso, trilha, codigo_trilha, carga_horaria_recurso
        ORDER BY recurso
    ";

    $stmt = $pdo_edu->prepare($cursos_query);
    $stmt->execute($params);
    $dados = $stmt->fetchAll();
    $filename_prefix = 'cursos_educacao';

} else {
    // Query para buscar todos os colaboradores (sem limite, excluindo APRENDIZ)
    $colaboradores_query = "
        SELECT
            cpf,
            usuario,
            email,
            funcao,
            codigo_unidade,
            hierarquia_unidade,
            data_admissao,
            superior_imediato,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            SUM(CASE WHEN situacao_trilha = 'Concluída' THEN 1 ELSE 0 END) as trilhas_concluidas,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
            MAX(data_importacao) as ultima_atualizacao
        FROM edu_relatorio_educacao
        WHERE funcao != 'APRENDIZ'" . (!empty($where_conditions) ? " AND " . implode(" AND ", $where_conditions) : "") . "
        GROUP BY cpf, usuario, email, funcao, codigo_unidade, hierarquia_unidade, data_admissao, superior_imediato
        ORDER BY usuario
    ";

    $stmt = $pdo_edu->prepare($colaboradores_query);
    $stmt->execute($params);
    $dados = $stmt->fetchAll();
    $filename_prefix = 'colaboradores_educacao';
}

// Função para formatar CPF
function formatarCpf($cpf) {
    if (strlen($cpf) == 11) {
        return substr($cpf, 0, 3) . '.' . substr($cpf, 3, 3) . '.' . substr($cpf, 6, 3) . '-' . substr($cpf, 9, 2);
    }
    return $cpf;
}

// Função para formatar data
function formatarData($data) {
    if (empty($data) || $data === '0000-00-00') return '';
    return date('d/m/Y', strtotime($data));
}

// Configurar headers para download do Excel
$filename = $filename_prefix . '_' . date('Y-m-d_H-i-s') . '.csv';
header('Content-Type: text/csv; charset=utf-8');
header('Content-Disposition: attachment; filename="' . $filename . '"');
header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
header('Pragma: public');

// Abrir output stream
$output = fopen('php://output', 'w');

// Adicionar BOM para UTF-8 (para Excel reconhecer acentos)
fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

// Cabeçalhos do CSV baseado no tipo
if ($tipo_exportacao === 'cursos') {
    $headers = [
        'Código do Curso',
        'Nome do Curso',
        'Trilha',
        'Código da Trilha',
        'Carga Horária',
        'Total Colaboradores',
        'Total Aprovados',
        'Total Concluídos',
        'Taxa Aprovação (%)',
        'Taxa Conclusão (%)',
        'Média de Notas',
        'Média Aproveitamento (%)',
        'Total Registros',
        'Última Atualização'
    ];
} else {
    $headers = [
        'CPF',
        'Nome',
        'Email',
        'Função',
        'Código Unidade',
        'Hierarquia Unidade',
        'Data Admissão',
        'Superior Imediato',
        'Total Trilhas',
        'Total Cursos',
        'Cursos Aprovados',
        'Trilhas Concluídas',
        'Média Aproveitamento (%)',
        'Última Atualização',
        // Campos da Intranet
        'Nome Intranet',
        'Email Intranet',
        'Agência',
        'Setor',
        'Função Intranet',
        'Status Intranet'
    ];
}

fputcsv($output, $headers, ';');

// Dados baseado no tipo de exportação
if ($tipo_exportacao === 'cursos') {
    foreach ($dados as $curso) {
        $percentual_aprovacao = $curso['total_colaboradores'] > 0 ?
            ($curso['total_aprovados'] / $curso['total_colaboradores']) * 100 : 0;
        $percentual_conclusao = $curso['total_colaboradores'] > 0 ?
            ($curso['total_concluidos'] / $curso['total_colaboradores']) * 100 : 0;

        $row = [
            $curso['codigo_recurso'],
            $curso['recurso'],
            $curso['trilha'],
            $curso['codigo_trilha'],
            $curso['carga_horaria_recurso'] ?: '',
            $curso['total_colaboradores'],
            $curso['total_aprovados'],
            $curso['total_concluidos'],
            number_format($percentual_aprovacao, 2),
            number_format($percentual_conclusao, 2),
            number_format($curso['media_notas'] ?? 0, 2),
            number_format($curso['media_aproveitamento'] ?? 0, 2),
            $curso['total_registros'],
            formatarData($curso['ultima_atualizacao'])
        ];

        fputcsv($output, $row, ';');
    }
} else {
    foreach ($dados as $colaborador) {
        // Buscar dados da Intranet
        $usuario_intranet = $mapa_usuarios_cpf[$colaborador['cpf']] ?? null;

        $row = [
            formatarCpf($colaborador['cpf']),
            $colaborador['usuario'],
            $colaborador['email'] ?: '',
            $colaborador['funcao'] ?: '',
            $colaborador['codigo_unidade'] ?: '',
            $colaborador['hierarquia_unidade'] ?: '',
            formatarData($colaborador['data_admissao']),
            $colaborador['superior_imediato'] ?: '',
            $colaborador['total_trilhas'],
            $colaborador['total_cursos'],
            $colaborador['cursos_aprovados'],
            $colaborador['trilhas_concluidas'],
            number_format($colaborador['media_aproveitamento'] ?? 0, 2),
            formatarData($colaborador['ultima_atualizacao']),
            // Dados da Intranet
            $usuario_intranet['nome'] ?? '',
            $usuario_intranet['email'] ?? '',
            $usuario_intranet['agencia'] ?? '',
            $usuario_intranet['nomeSetor'] ?? '',
            $usuario_intranet['nomeFuncao'] ?? '',
            $usuario_intranet ? 'Ativo' : 'Não encontrado'
        ];

        fputcsv($output, $row, ';');
    }
}

fclose($output);
exit;
?>
