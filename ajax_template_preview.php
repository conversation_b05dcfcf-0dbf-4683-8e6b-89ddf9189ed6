<?php
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'edu_auth_check.php';
require_once 'classes/EmailManager.php';
require_once 'classes/IntranetAPI.php';

header('Content-Type: application/json');

try {
    $template_id = $_GET['template_id'] ?? null;
    $cpf = $_GET['cpf'] ?? null;
    
    if (!$template_id || !$cpf) {
        throw new Exception("Template ID e CPF são obrigatórios");
    }
    
    // Buscar template
    $emailManager = new EmailManager($pdo_edu);
    $template = $emailManager->buscarTemplate($template_id);
    
    if (!$template) {
        throw new Exception("Template não encontrado");
    }
    
    // Buscar dados do colaborador
    $cpf = str_pad(preg_replace('/[^0-9]/', '', $cpf), 11, '0', STR_PAD_LEFT);
    
    $stmt = $pdo_edu->prepare("
        SELECT
            cpf,
            usuario,
            email,
            funcao,
            codigo_unidade,
            hierarquia_unidade,
            data_admissao,
            superior_imediato,
            COUNT(DISTINCT trilha) as total_trilhas,
            COUNT(DISTINCT recurso) as total_cursos,
            SUM(CASE WHEN aprovacao = 'Sim' THEN 1 ELSE 0 END) as cursos_aprovados,
            SUM(CASE WHEN situacao_trilha = 'Concluída' THEN 1 ELSE 0 END) as trilhas_concluidas,
            AVG(CASE WHEN aproveitamento > 0 THEN aproveitamento ELSE NULL END) as media_aproveitamento,
            MAX(data_importacao) as ultima_atualizacao
        FROM edu_relatorio_educacao
        WHERE cpf = ? AND funcao != 'APRENDIZ'
        GROUP BY cpf, usuario, email, funcao, codigo_unidade, hierarquia_unidade, data_admissao, superior_imediato
    ");
    $stmt->execute([$cpf]);
    $colaborador = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$colaborador) {
        throw new Exception("Colaborador não encontrado ou possui função APRENDIZ");
    }
    
    // Buscar dados da Intranet - APENAS USUÁRIOS ATIVOS
    $api = new IntranetAPI();
    $usuario_intranet = null;
    $usuarios_intranet = $api->listarUsuariosAtivos();
    if ($usuarios_intranet !== false) {
        foreach ($usuarios_intranet as $usuario) {
            if (!empty($usuario['cpf'])) {
                $cpf_intranet = str_pad(preg_replace('/[^0-9]/', '', $usuario['cpf']), 11, '0', STR_PAD_LEFT);
                if ($cpf_intranet === $cpf) {
                    $usuario_intranet = $usuario;
                    break;
                }
            }
        }
    }
    
    // Buscar agências da Intranet
    $agencias_intranet = $api->listarAgencias();
    $mapa_agencias = [];
    if ($agencias_intranet !== false) {
        foreach ($agencias_intranet as $agencia) {
            if (!empty($agencia['id'])) {
                $mapa_agencias[$agencia['id']] = $agencia;
            }
        }
    }
    
    // Preparar dados do colaborador para processamento
    $dados_colaborador = [
        'cpf' => $colaborador['cpf'],
        'nome' => $usuario_intranet['nome'] ?? $colaborador['usuario'],
        'email' => $usuario_intranet['email'] ?? $colaborador['email'],
        'funcao' => $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'],
        'pa' => $colaborador['codigo_unidade'],
        'total_cursos' => $colaborador['total_cursos'],
        'cursos_aprovados' => $colaborador['cursos_aprovados'],
        'cursos_a_vencer' => 0, // Será calculado
        'cursos_vencidos' => 0  // Será calculado
    ];
    
    // Buscar informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        }
    }
    $dados_colaborador['pa'] = $agencia_info;
    
    // Buscar configurações de prazos personalizados
    $stmt_prazos = $pdo_edu->query("SELECT * FROM edu_prazos_personalizados WHERE prazo_personalizado_ativo = 1");
    $prazos_config = [];
    foreach ($stmt_prazos->fetchAll(PDO::FETCH_ASSOC) as $config) {
        $key = $config['codigo_trilha'] . '|' . $config['codigo_recurso'];
        $prazos_config[$key] = $config;
    }

    // Buscar todos os cursos do colaborador para calcular métricas corretas
    $stmt_cursos_detalhados = $pdo_edu->prepare("
        SELECT
            codigo_trilha,
            codigo_recurso,
            aprovacao,
            data_conclusao,
            concluir_trilha_ate,
            data_admissao,
            andamento_etapa
        FROM edu_relatorio_educacao
        WHERE cpf = ?
        ORDER BY trilha, recurso
    ");
    $stmt_cursos_detalhados->execute([$cpf]);
    $cursos_raw = $stmt_cursos_detalhados->fetchAll(PDO::FETCH_ASSOC);

    // Calcular métricas corretas baseadas nos prazos personalizados (mesma lógica do card)
    $cursos_vencidos_corretos = 0;
    $cursos_a_vencer_corretos = 0;

    foreach ($cursos_raw as $curso) {
        $status_final = calcularStatusPrazoOtimizado($curso, $prazos_config);

        if ($status_final === 'vencido') {
            $cursos_vencidos_corretos++;
        } elseif ($status_final === 'a_vencer') {
            $cursos_a_vencer_corretos++;
        }
    }

    $dados_colaborador['cursos_a_vencer'] = $cursos_a_vencer_corretos;
    $dados_colaborador['cursos_vencidos'] = $cursos_vencidos_corretos;

    // Processar template com dados do colaborador
    $conteudo = $emailManager->processarVariaveis($template, $dados_colaborador);
    
    echo json_encode([
        'success' => true,
        'preview' => [
            'assunto' => $conteudo['assunto'],
            'corpo_html' => $conteudo['corpo_html']
        ]
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}

// Função para calcular status de prazo otimizado (mesma lógica do analise_colaboradores.php)
function calcularStatusPrazoOtimizado($curso, $prazos_config) {
    $key = $curso['codigo_trilha'] . '|' . $curso['codigo_recurso'];

    // Se tem prazo personalizado e colaborador é elegível
    if (isset($prazos_config[$key]) && $curso['data_admissao'] > '2023-01-01') {
        $config = $prazos_config[$key];
        $primeiro_prazo = (int)$config['primeiro_prazo_dias'];
        $renovacao_prazo = !empty($config['renovacao_prazo_dias']) ? (int)$config['renovacao_prazo_dias'] : 0;

        if ($primeiro_prazo > 0) {
            // Lógica simplificada para prazos personalizados
            if (!empty($curso['data_conclusao']) && $curso['data_conclusao'] !== '0000-00-00') {
                if ($renovacao_prazo <= 0) {
                    return 'concluido_sem_renovacao'; // Sem renovação
                }
                // Calcular prazo de renovação
                $data_conclusao = new DateTime($curso['data_conclusao']);
                $prazo_renovacao = clone $data_conclusao;
                $prazo_renovacao->add(new DateInterval('P' . $renovacao_prazo . 'D'));
                $hoje = new DateTime();

                if ($prazo_renovacao < $hoje) {
                    return 'vencido';
                } elseif ($prazo_renovacao <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            } else {
                // Primeiro prazo
                $data_admissao = new DateTime($curso['data_admissao']);
                $primeiro_prazo_data = clone $data_admissao;
                $primeiro_prazo_data->add(new DateInterval('P' . $primeiro_prazo . 'D'));
                $hoje = new DateTime();

                if ($primeiro_prazo_data < $hoje) {
                    return 'vencido';
                } elseif ($primeiro_prazo_data <= (clone $hoje)->add(new DateInterval('P30D'))) {
                    return 'a_vencer';
                }
                return 'em_dia';
            }
        }
    }

    // Usar lógica básica de prazo padrão
    if (!empty($curso['concluir_trilha_ate'])) {
        $hoje = new DateTime();
        $prazo = new DateTime($curso['concluir_trilha_ate']);

        if ($curso['aprovacao'] === 'Sim') {
            return 'aprovado'; // Curso já aprovado
        }

        if ($prazo < $hoje) {
            return 'vencido';
        } elseif ($prazo <= (clone $hoje)->add(new DateInterval('P30D'))) {
            return 'a_vencer';
        }
        return 'em_dia';
    }

    return 'em_dia';
}
?>
