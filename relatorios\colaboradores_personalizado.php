<?php
// Relatório Personalizado de Colaboradores
// Este arquivo gera relatórios baseados em múltiplos status selecionados

// Obter status selecionados
$status_selecionados = [];

// Verificar como os status estão chegando
if (isset($filtros['status']) && !empty($filtros['status'])) {
    if (is_array($filtros['status'])) {
        $status_selecionados = $filtros['status'];
    } else {
        // Se vier como string separada por vírgula
        $status_selecionados = explode(',', $filtros['status']);
    }
} elseif (isset($_GET['status']) && !empty($_GET['status'])) {
    if (is_array($_GET['status'])) {
        $status_selecionados = $_GET['status'];
    } else {
        // Se vier como string separada por vírgula
        $status_selecionados = explode(',', $_GET['status']);
    }
}

// Limpar array de valores vazios e trim
$status_selecionados = array_filter(array_map('trim', $status_selecionados));

if (empty($status_selecionados)) {
    echo '<h1>Nenhum status selecionado</h1>';
    echo '<p>Por favor, selecione pelo menos um status para gerar o relatório personalizado.</p>';
    return;
}

// Construir query base
$where_conditions = [];
$params = [];

if (!empty($filtros['trilha'])) {
    $where_conditions[] = "trilha LIKE ?";
    $params[] = '%' . $filtros['trilha'] . '%';
}

if (!empty($filtros['funcao'])) {
    $where_conditions[] = "funcao = ?";
    $params[] = $filtros['funcao'];
}

// Query para buscar todos os colaboradores (excluindo APRENDIZ)
$colaboradores_query = "
    SELECT
        cpf,
        MAX(usuario) as usuario,
        MAX(email) as email,
        MAX(funcao) as funcao,
        MAX(codigo_unidade) as codigo_unidade,
        MAX(data_admissao) as data_admissao,
        MAX(superior_imediato) as superior_imediato,
        COUNT(DISTINCT trilha) as total_trilhas,
        COUNT(DISTINCT CONCAT(codigo_trilha, '|', codigo_recurso)) as total_cursos,
        MAX(data_importacao) as ultima_atualizacao
    FROM edu_relatorio_educacao
    WHERE funcao != 'APRENDIZ'" . (!empty($where_conditions) ? " AND " . implode(" AND ", $where_conditions) : "") . "
    GROUP BY cpf
    ORDER BY MAX(usuario)
";

$stmt_colaboradores = $pdo_edu->prepare($colaboradores_query);
$stmt_colaboradores->execute($params);
$todos_colaboradores = $stmt_colaboradores->fetchAll();

// Filtrar colaboradores que possuem cursos com os status selecionados
$colaboradores_filtrados = [];

foreach ($todos_colaboradores as $colaborador) {
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    // Filtrar por PA se especificado
    if (!empty($filtros['pa'])) {
        if (!$usuario_intranet || $usuario_intranet['agencia'] != $filtros['pa']) {
            continue;
        }
    }
    
    $cursos_colaborador = buscarCursosColaborador($colaborador['cpf'], $pdo_edu, $prazos_config);
    $cursos_com_status = [];
    $status_encontrados = [];
    
    // Verificar filtro de período se especificado
    $tem_curso_no_periodo = true;
    if (!empty($filtros['periodo'])) {
        $tem_curso_no_periodo = false;
        foreach ($cursos_colaborador as $curso) {
            if (!empty($curso['prazo_calculado'])) {
                $prazo_curso = new DateTime($curso['prazo_calculado']);
                if (verificarCursoNoPeriodo($prazo_curso, $filtros['periodo'])) {
                    $tem_curso_no_periodo = true;
                    break;
                }
            }
        }
    }
    
    if (!$tem_curso_no_periodo) continue;
    
    // Verificar status dos cursos
    foreach ($cursos_colaborador as $curso) {
        $status_curso = '';

        if ($curso['aprovacao'] === 'Sim') {
            $status_curso = 'aprovado';
        } elseif ($curso['status_prazo'] === 'vencido') {
            $status_curso = 'vencido';
        } elseif ($curso['status_prazo'] === 'a_vencer') {
            $status_curso = 'a_vencer';
        } elseif (!empty($curso['andamento_etapa']) && $curso['aprovacao'] !== 'Sim') {
            $status_curso = 'em_andamento';
        }



        if (in_array($status_curso, $status_selecionados)) {
            $cursos_com_status[] = $curso;
            $status_encontrados[$status_curso] = true;
        }
    }
    
    // Verificar se o colaborador tem cursos com QUALQUER UM dos status selecionados
    if (!empty($cursos_com_status)) {
        $colaborador['cursos_filtrados'] = $cursos_com_status;
        $colaborador['total_filtrados'] = count($cursos_com_status);
        $colaboradores_filtrados[] = $colaborador;
    }
}

// Cabeçalho do relatório
echo '<table border="1" style="border-collapse: collapse;">';
echo '<tr style="background-color: #6f42c1; color: white; font-weight: bold;">';
echo '<td colspan="15" style="padding: 10px; text-align: center; font-size: 16px;">';
echo 'RELATÓRIO PERSONALIZADO DE COLABORADORES';
echo '</td>';
echo '</tr>';

echo '<tr style="background-color: #f8f9fa; font-weight: bold;">';
echo '<td style="padding: 8px;">Gerado em:</td>';
echo '<td colspan="14" style="padding: 8px;">' . date('d/m/Y H:i:s') . '</td>';
echo '</tr>';

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Status Selecionados:</td>';
echo '<td colspan="14" style="padding: 8px;">';
$status_nomes = [
    'aprovado' => '✅ Aprovado',
    'em_andamento' => '🔄 Em Andamento', 
    'a_vencer' => '⏰ A Vencer',
    'vencido' => '🔴 Vencido'
];
$status_texto = [];
foreach ($status_selecionados as $status) {
    $status_texto[] = $status_nomes[$status] ?? $status;
}
echo implode(' + ', $status_texto);
echo '</td>';
echo '</tr>';

if (!empty($filtros['trilha']) || !empty($filtros['funcao']) || !empty($filtros['periodo']) || !empty($filtros['pa'])) {
    echo '<tr style="background-color: #e9ecef;">';
    echo '<td style="padding: 8px; font-weight: bold;">Filtros Adicionais:</td>';
    echo '<td colspan="14" style="padding: 8px;">';
    $filtros_texto = [];
    if (!empty($filtros['trilha'])) $filtros_texto[] = "Trilha: " . $filtros['trilha'];
    if (!empty($filtros['funcao'])) $filtros_texto[] = "Função: " . $filtros['funcao'];
    if (!empty($filtros['periodo'])) $filtros_texto[] = "Período: " . $filtros['periodo'];
    if (!empty($filtros['pa'])) {
        $agencia_nome = isset($mapa_agencias[$filtros['pa']]) ? 
            $mapa_agencias[$filtros['pa']]['numero'] . ' - ' . $mapa_agencias[$filtros['pa']]['nome'] : 
            $filtros['pa'];
        $filtros_texto[] = "PA: " . $agencia_nome;
    }
    echo implode(' | ', $filtros_texto);
    echo '</td>';
    echo '</tr>';
}

echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Colaboradores Encontrados:</td>';
echo '<td colspan="14" style="padding: 8px; color: #6f42c1; font-weight: bold;">' . count($colaboradores_filtrados) . '</td>';
echo '</tr>';

$total_cursos_filtrados = array_sum(array_column($colaboradores_filtrados, 'total_filtrados'));
echo '<tr style="background-color: #e9ecef;">';
echo '<td style="padding: 8px; font-weight: bold;">Total de Cursos:</td>';
echo '<td colspan="14" style="padding: 8px; color: #6f42c1; font-weight: bold;">' . $total_cursos_filtrados . '</td>';
echo '</tr>';

echo '<tr><td colspan="15" style="padding: 5px;"></td></tr>'; // Espaçamento

// Cabeçalhos das colunas
echo '<tr style="background-color: #6f42c1; color: white; font-weight: bold;">';
echo '<td style="padding: 8px; text-align: center;">CPF</td>';
echo '<td style="padding: 8px; text-align: center;">Nome</td>';
echo '<td style="padding: 8px; text-align: center;">E-mail</td>';
echo '<td style="padding: 8px; text-align: center;">Função</td>';
echo '<td style="padding: 8px; text-align: center;">PA/Agência</td>';
echo '<td style="padding: 8px; text-align: center;">Setor</td>';
echo '<td style="padding: 8px; text-align: center;">Superior Imediato</td>';
echo '<td style="padding: 8px; text-align: center;">Qtd Cursos</td>';
echo '<td style="padding: 8px; text-align: center;">Trilha</td>';
echo '<td style="padding: 8px; text-align: center;">Curso</td>';
echo '<td style="padding: 8px; text-align: center;">Status</td>';
echo '<td style="padding: 8px; text-align: center;">Prazo</td>';
echo '<td style="padding: 8px; text-align: center;">Dias</td>';
echo '<td style="padding: 8px; text-align: center;">Andamento</td>';
echo '<td style="padding: 8px; text-align: center;">Nota/Aproveitamento</td>';
echo '</tr>';

// Dados dos colaboradores
$linha = 0;
foreach ($colaboradores_filtrados as $colaborador) {
    $linha++;
    $cpf_normalizado = str_pad(preg_replace('/[^0-9]/', '', $colaborador['cpf']), 11, '0', STR_PAD_LEFT);
    $usuario_intranet = $mapa_usuarios_cpf[$cpf_normalizado] ?? null;
    
    // Informações da intranet
    $nome_exibir = $usuario_intranet['nome'] ?? $colaborador['usuario'];
    $email_exibir = $usuario_intranet['email'] ?? $colaborador['email'] ?? 'N/A';
    $funcao_exibir = $usuario_intranet['nomeFuncao'] ?? $colaborador['funcao'] ?? 'N/A';
    $setor_exibir = $usuario_intranet['nomeSetor'] ?? 'N/A';
    
    // Informações da agência
    $agencia_info = 'N/A';
    if ($usuario_intranet && !empty($usuario_intranet['agencia'])) {
        $agencia_id = $usuario_intranet['agencia'];
        if (isset($mapa_agencias[$agencia_id])) {
            $agencia_data = $mapa_agencias[$agencia_id];
            $agencia_info = $agencia_data['numero'] . ' - ' . $agencia_data['nome'];
        } else {
            $agencia_info = $agencia_id;
        }
    }
    
    $primeira_linha = true;
    foreach ($colaborador['cursos_filtrados'] as $curso) {
        $cor_linha = ($linha % 2 == 0) ? '#f8f9fa' : '#ffffff';
        echo '<tr style="background-color: ' . $cor_linha . ';">';
        
        if ($primeira_linha) {
            // Dados do colaborador (apenas na primeira linha)
            echo '<td style="padding: 6px;">' . formatarCpf($colaborador['cpf']) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($nome_exibir) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($email_exibir) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($funcao_exibir) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($agencia_info) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($setor_exibir) . '</td>';
            echo '<td style="padding: 6px;">' . htmlspecialchars($colaborador['superior_imediato'] ?? 'N/A') . '</td>';
            echo '<td style="padding: 6px; text-align: center; font-weight: bold; color: #6f42c1;">' . $colaborador['total_filtrados'] . '</td>';
            $primeira_linha = false;
        } else {
            // Células vazias para as outras linhas do mesmo colaborador
            echo '<td colspan="8" style="padding: 6px; border-left: none;"></td>';
        }
        
        // Dados do curso
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['trilha']) . '</td>';
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['recurso']) . '</td>';
        
        // Status do curso
        $status_texto = 'Pendente';
        $cor_status = '#6c757d';
        
        if ($curso['aprovacao'] === 'Sim') {
            $status_texto = '✅ Aprovado';
            $cor_status = '#28a745';
        } elseif ($curso['status_prazo'] === 'vencido') {
            $status_texto = '🔴 Vencido';
            $cor_status = '#dc3545';
        } elseif ($curso['status_prazo'] === 'a_vencer') {
            $status_texto = '⏰ A Vencer';
            $cor_status = '#fd7e14';
        } elseif (!empty($curso['andamento_etapa'])) {
            $status_texto = '🔄 Em Andamento';
            $cor_status = '#007bff';
        }
        
        echo '<td style="padding: 6px; color: ' . $cor_status . '; font-weight: bold;">' . $status_texto . '</td>';
        
        // Prazo
        echo '<td style="padding: 6px; text-align: center;">' . 
             ($curso['prazo_calculado'] ? date('d/m/Y', strtotime($curso['prazo_calculado'])) : 'N/A') . '</td>';
        
        // Dias
        $dias_texto = 'N/A';
        if ($curso['dias_prazo'] !== null) {
            if ($curso['dias_prazo'] > 0) {
                $dias_texto = $curso['dias_prazo'] . ' restantes';
            } else {
                $dias_texto = abs($curso['dias_prazo']) . ' em atraso';
            }
        }
        echo '<td style="padding: 6px; text-align: center;">' . $dias_texto . '</td>';
        
        // Andamento
        echo '<td style="padding: 6px;">' . htmlspecialchars($curso['andamento_etapa'] ?? 'N/A') . '</td>';
        
        // Nota/Aproveitamento
        $nota_texto = 'N/A';
        if ($curso['nota_recurso']) {
            $nota_texto = 'Nota: ' . $curso['nota_recurso'];
        } elseif ($curso['aproveitamento']) {
            $nota_texto = 'Aproveitamento: ' . $curso['aproveitamento'] . '%';
        }
        echo '<td style="padding: 6px; text-align: center;">' . $nota_texto . '</td>';
        
        echo '</tr>';
    }
}

echo '</table>';
?>
